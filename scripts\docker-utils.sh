#!/bin/bash

# Script de utilidades para Docker de Botica Fray Martin
# Uso: ./scripts/docker-utils.sh [comando]

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para mostrar ayuda
show_help() {
    echo -e "${BLUE}Botica Fray Martin - Docker Utilities${NC}"
    echo ""
    echo "Comandos disponibles:"
    echo ""
    echo -e "${GREEN}Producción:${NC}"
    echo "  build-prod     - Construir imágenes para producción"
    echo "  up-prod        - Levantar servicios en modo producción"
    echo "  down-prod      - Detener servicios de producción"
    echo "  logs-prod      - Ver logs de producción"
    echo ""
    echo -e "${YELLOW}Desarrollo:${NC}"
    echo "  build-dev      - Construir imágenes para desarrollo"
    echo "  up-dev         - Levantar servicios en modo desarrollo"
    echo "  down-dev       - Detener servicios de desarrollo"
    echo "  logs-dev       - Ver logs de desarrollo"
    echo ""
    echo -e "${BLUE}Utilidades:${NC}"
    echo "  clean          - Limpiar imágenes y volúmenes no utilizados"
    echo "  clean-all      - Limpiar todo (¡CUIDADO! Elimina todos los datos)"
    echo "  status         - Mostrar estado de los contenedores"
    echo "  shell-backend  - Abrir shell en el contenedor backend"
    echo "  shell-frontend - Abrir shell en el contenedor frontend"
    echo "  db-backup      - Crear backup de la base de datos"
    echo "  db-restore     - Restaurar backup de la base de datos"
    echo ""
}

# Función para verificar si Docker está corriendo
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Error: Docker no está corriendo${NC}"
        exit 1
    fi
}

# Función para verificar si existe .env
check_env() {
    if [ ! -f .env ]; then
        echo -e "${YELLOW}Advertencia: No se encontró archivo .env${NC}"
        echo "Copiando .env.example a .env..."
        cp .env.example .env
        echo -e "${GREEN}Archivo .env creado. Por favor, configura las variables necesarias.${NC}"
    fi
}

# Comandos de producción
build_prod() {
    echo -e "${GREEN}Construyendo imágenes para producción...${NC}"
    docker compose build --no-cache
}

up_prod() {
    echo -e "${GREEN}Levantando servicios en modo producción...${NC}"
    docker compose up -d
    echo -e "${GREEN}Servicios iniciados. Accede a:${NC}"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:3001/api"
}

down_prod() {
    echo -e "${YELLOW}Deteniendo servicios de producción...${NC}"
    docker compose down
}

logs_prod() {
    docker compose logs -f
}

# Comandos de desarrollo
build_dev() {
    echo -e "${GREEN}Construyendo imágenes para desarrollo...${NC}"
    docker compose -f docker-compose.dev.yml build --no-cache
}

up_dev() {
    echo -e "${GREEN}Levantando servicios en modo desarrollo...${NC}"
    docker compose -f docker-compose.dev.yml up -d
    echo -e "${GREEN}Servicios de desarrollo iniciados. Accede a:${NC}"
    echo "  Frontend: http://localhost:3000 (con hot reload)"
    echo "  Backend API: http://localhost:3001/api (con nodemon)"
}

down_dev() {
    echo -e "${YELLOW}Deteniendo servicios de desarrollo...${NC}"
    docker compose -f docker-compose.dev.yml down
}

logs_dev() {
    docker compose -f docker-compose.dev.yml logs -f
}

# Utilidades
clean() {
    echo -e "${YELLOW}Limpiando imágenes y volúmenes no utilizados...${NC}"
    docker system prune -f
    docker volume prune -f
}

clean_all() {
    echo -e "${RED}¡ADVERTENCIA! Esto eliminará TODOS los datos de Docker.${NC}"
    read -p "¿Estás seguro? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${RED}Eliminando todos los contenedores, imágenes y volúmenes...${NC}"
        docker compose down -v
        docker compose -f docker-compose.dev.yml down -v
        docker system prune -a -f --volumes
    else
        echo "Operación cancelada."
    fi
}

status() {
    echo -e "${BLUE}Estado de los contenedores:${NC}"
    docker ps -a --filter "name=botica"
    echo ""
    echo -e "${BLUE}Uso de volúmenes:${NC}"
    docker volume ls --filter "name=botica"
}

shell_backend() {
    if docker ps --filter "name=botica-backend" --filter "status=running" | grep -q botica-backend; then
        docker exec -it botica-backend sh
    else
        echo -e "${RED}El contenedor backend no está corriendo${NC}"
    fi
}

shell_frontend() {
    if docker ps --filter "name=botica-frontend" --filter "status=running" | grep -q botica-frontend; then
        docker exec -it botica-frontend sh
    else
        echo -e "${RED}El contenedor frontend no está corriendo${NC}"
    fi
}

db_backup() {
    echo -e "${GREEN}Creando backup de la base de datos...${NC}"
    timestamp=$(date +%Y%m%d_%H%M%S)
    docker exec botica-postgres pg_dump -U postgres botica_fray_martin > "backup_${timestamp}.sql"
    echo -e "${GREEN}Backup creado: backup_${timestamp}.sql${NC}"
}

db_restore() {
    if [ -z "$1" ]; then
        echo -e "${RED}Error: Especifica el archivo de backup${NC}"
        echo "Uso: $0 db-restore backup_file.sql"
        exit 1
    fi
    
    if [ ! -f "$1" ]; then
        echo -e "${RED}Error: Archivo de backup no encontrado: $1${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}Restaurando base de datos desde $1...${NC}"
    docker exec -i botica-postgres psql -U postgres botica_fray_martin < "$1"
    echo -e "${GREEN}Base de datos restaurada${NC}"
}

# Verificar Docker y .env
check_docker
check_env

# Procesar comando
case "$1" in
    "build-prod")
        build_prod
        ;;
    "up-prod")
        up_prod
        ;;
    "down-prod")
        down_prod
        ;;
    "logs-prod")
        logs_prod
        ;;
    "build-dev")
        build_dev
        ;;
    "up-dev")
        up_dev
        ;;
    "down-dev")
        down_dev
        ;;
    "logs-dev")
        logs_dev
        ;;
    "clean")
        clean
        ;;
    "clean-all")
        clean_all
        ;;
    "status")
        status
        ;;
    "shell-backend")
        shell_backend
        ;;
    "shell-frontend")
        shell_frontend
        ;;
    "db-backup")
        db_backup
        ;;
    "db-restore")
        db_restore "$2"
        ;;
    *)
        show_help
        ;;
esac

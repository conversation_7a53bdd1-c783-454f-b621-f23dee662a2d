FROM node:18-alpine

# Instalar dependencias del sistema necesarias
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    vips-dev \
    dumb-init

# Crear directorio de la aplicación
WORKDIR /app

# Configurar npm para optimizar instalación
RUN npm config set fund false
RUN npm config set audit false

# Copiar archivos de package.json primero (para mejor cache de Docker)
COPY package*.json ./

# Verificar que los archivos se copiaron correctamente
RUN ls -la package*

# Instalar TODAS las dependencias (incluyendo dev) para desarrollo
RUN npm install --no-audit --no-fund

# Crear directorio para uploads
RUN mkdir -p uploads/products uploads/temp

# Exponer puerto
EXPOSE 3001

# Variables de entorno por defecto para desarrollo
ENV NODE_ENV=development
ENV PORT=3001

# Comando de inicio con dumb-init para mejor manejo de señales
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "run", "dev"]

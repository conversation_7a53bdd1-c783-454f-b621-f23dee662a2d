# Multi-stage build para optimizar tamaño de imagen final
FROM node:18-alpine as build

# Instalar dependencias del sistema
RUN apk add --no-cache git python3 make g++

# Establecer directorio de trabajo
WORKDIR /app

# Configurar npm para optimizar instalación
RUN npm config set fund false
RUN npm config set audit false

# Argumentos de build para variables de entorno
ARG REACT_APP_API_URL=http://localhost:3001/api
ARG REACT_APP_STRIPE_PUBLISHABLE_KEY

# Copiar archivos de dependencias primero (para mejor cache de Docker)
COPY package*.json ./

# Instalar dependencias con cache optimizado
RUN npm ci --silent --no-audit --no-fund

# Copiar código fuente (excluyendo node_modules gracias a .dockerignore)
COPY . .

# Configurar variables de entorno para el build
ENV NODE_ENV=production
ENV GENERATE_SOURCEMAP=false
ENV INLINE_RUNTIME_CHUNK=false
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_STRIPE_PUBLISHABLE_KEY=$REACT_APP_STRIPE_PUBLISHABLE_KEY

# Build de la aplicación con optimizaciones
RUN npm run build

# Etapa de producción
FROM nginx:alpine

# Copiar archivos estáticos del build
COPY --from=build /app/build /usr/share/nginx/html

# Copiar configuración personalizada de nginx
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Exponer puerto
EXPOSE 3000

# Comando por defecto
CMD ["nginx", "-g", "daemon off;"]

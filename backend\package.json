{"name": "botica-fray-martin-backend", "version": "1.0.0", "description": "Backend API para Botica Fray Martin E-commerce", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build process required for Node.js'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cors": "^2.8.5", "cron": "^3.1.6", "csv-parse": "^5.5.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "fs": "^0.0.1-security", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "openai": "^4.20.1", "path": "^0.12.7", "pg": "^8.11.3", "redis": "^4.6.10", "sequelize": "^6.35.2", "sharp": "^0.33.0", "socket.io": "^4.7.4", "stripe": "^14.9.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "keywords": ["pharmacy", "ecommerce", "erp", "crm", "srm", "nodejs", "express", "postgresql"], "author": "Botica Fray Martin", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
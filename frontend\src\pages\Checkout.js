import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  TextField,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import { selectCartItems, selectCartTotals, clearCart } from '../store/slices/cartSlice';
import api from '../services/api';

const steps = ['Información de envío', 'Método de pago', 'Confirmación'];

const Checkout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const cartItems = useSelector(selectCartItems);
  const cartTotals = useSelector(selectCartTotals);

  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Formulario de datos
  const [formData, setFormData] = useState({
    // Información personal
    firstName: '',
    lastName: '',
    email: '',
    phone: '',

    // Dirección de envío
    address: '',
    city: '',
    district: '',
    postalCode: '',
    reference: '',

    // Método de pago
    paymentMethod: 'cash_on_delivery',

    // Notas adicionales
    notes: ''
  });

  // Redirigir si el carrito está vacío
  useEffect(() => {
    if (cartItems.length === 0 && !success) {
      navigate('/carrito');
    }
  }, [cartItems, navigate, success]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNext = () => {
    if (activeStep === 0 && !validateShippingInfo()) {
      return;
    }
    if (activeStep === 1 && !validatePaymentInfo()) {
      return;
    }
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const validateShippingInfo = () => {
    const required = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'district'];
    const missing = required.filter(field => !formData[field].trim());

    if (missing.length > 0) {
      setError('Por favor completa todos los campos obligatorios');
      return false;
    }

    // Validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Por favor ingresa un email válido');
      return false;
    }

    setError('');
    return true;
  };

  const validatePaymentInfo = () => {
    if (!formData.paymentMethod) {
      setError('Por favor selecciona un método de pago');
      return false;
    }
    setError('');
    return true;
  };

  const handleSubmitOrder = async () => {
    setLoading(true);
    setError('');

    try {
      const orderData = {
        items: cartItems.map(item => ({
          product_id: item.id,
          quantity: item.quantity,
          unit_price: item.price
        })),
        shipping_address: {
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          city: formData.city,
          district: formData.district,
          postal_code: formData.postalCode,
          reference: formData.reference
        },
        payment_method: formData.paymentMethod,
        notes: formData.notes,
        totals: cartTotals
      };

      const response = await api.post('/orders', orderData);

      if (response.data.success) {
        setSuccess(true);
        dispatch(clearCart());
        // Redirigir a página de confirmación después de 3 segundos
        setTimeout(() => {
          navigate(`/pedidos/${response.data.data.id}`);
        }, 3000);
      }
    } catch (error) {
      setError(error.response?.data?.message || 'Error al procesar el pedido');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <>
        <Helmet>
          <title>Pedido Confirmado - Botica Fray Martin</title>
        </Helmet>
        <Container maxWidth="md" sx={{ py: 8, textAlign: 'center' }}>
          <Typography variant="h4" color="success.main" gutterBottom>
            ¡Pedido Confirmado!
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Tu pedido ha sido procesado exitosamente. Recibirás un email de confirmación en breve.
          </Typography>
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 2 }}>
            Redirigiendo a los detalles del pedido...
          </Typography>
        </Container>
      </>
    );
  }

  return (
    <>
      <Helmet>
        <title>Checkout - Botica Fray Martin</title>
      </Helmet>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Finalizar Compra
        </Typography>

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={4}>
          {/* Formulario principal */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3 }}>
              {activeStep === 0 && (
                <ShippingForm formData={formData} onChange={handleInputChange} />
              )}
              {activeStep === 1 && (
                <PaymentForm formData={formData} onChange={handleInputChange} />
              )}
              {activeStep === 2 && (
                <OrderSummary formData={formData} cartItems={cartItems} cartTotals={cartTotals} />
              )}

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                <Button
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Atrás
                </Button>

                {activeStep === steps.length - 1 ? (
                  <Button
                    variant="contained"
                    onClick={handleSubmitOrder}
                    disabled={loading}
                    startIcon={loading && <CircularProgress size={20} />}
                  >
                    {loading ? 'Procesando...' : 'Confirmar Pedido'}
                  </Button>
                ) : (
                  <Button variant="contained" onClick={handleNext}>
                    Siguiente
                  </Button>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Resumen del pedido */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, position: 'sticky', top: 20 }}>
              <Typography variant="h6" gutterBottom>
                Resumen del Pedido
              </Typography>

              <List dense>
                {cartItems.map((item) => (
                  <ListItem key={item.id} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar
                        src={item.image}
                        alt={item.name}
                        variant="rounded"
                        sx={{ width: 50, height: 50 }}
                      />
                    </ListItemAvatar>
                    <ListItemText
                      primary={item.name}
                      secondary={`Cantidad: ${item.quantity}`}
                    />
                    <Typography variant="body2">
                      S/ {(item.price * item.quantity).toFixed(2)}
                    </Typography>
                  </ListItem>
                ))}
              </List>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Subtotal:</Typography>
                <Typography>S/ {cartTotals.subtotal.toFixed(2)}</Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>IGV (18%):</Typography>
                <Typography>S/ {cartTotals.tax.toFixed(2)}</Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Envío:</Typography>
                <Typography>
                  {cartTotals.shipping === 0 ? 'Gratis' : `S/ ${cartTotals.shipping.toFixed(2)}`}
                </Typography>
              </Box>

              <Divider sx={{ my: 1 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6">Total:</Typography>
                <Typography variant="h6" color="primary">
                  S/ {cartTotals.total.toFixed(2)}
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  );
};

// Componente para información de envío
const ShippingForm = ({ formData, onChange }) => (
  <Box>
    <Typography variant="h6" gutterBottom>
      Información de Envío
    </Typography>

    <Grid container spacing={2}>
      <Grid item xs={12} sm={6}>
        <TextField
          required
          fullWidth
          name="firstName"
          label="Nombres"
          value={formData.firstName}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          required
          fullWidth
          name="lastName"
          label="Apellidos"
          value={formData.lastName}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          required
          fullWidth
          name="email"
          label="Email"
          type="email"
          value={formData.email}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          required
          fullWidth
          name="phone"
          label="Teléfono"
          value={formData.phone}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          required
          fullWidth
          name="address"
          label="Dirección"
          value={formData.address}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <TextField
          required
          fullWidth
          name="city"
          label="Ciudad"
          value={formData.city}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <TextField
          required
          fullWidth
          name="district"
          label="Distrito"
          value={formData.district}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <TextField
          fullWidth
          name="postalCode"
          label="Código Postal"
          value={formData.postalCode}
          onChange={onChange}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          name="reference"
          label="Referencia (opcional)"
          multiline
          rows={2}
          value={formData.reference}
          onChange={onChange}
        />
      </Grid>
    </Grid>
  </Box>
);

// Componente para método de pago
const PaymentForm = ({ formData, onChange }) => (
  <Box>
    <Typography variant="h6" gutterBottom>
      Método de Pago
    </Typography>

    <FormControl component="fieldset" sx={{ mt: 2 }}>
      <FormLabel component="legend">Selecciona tu método de pago preferido</FormLabel>
      <RadioGroup
        name="paymentMethod"
        value={formData.paymentMethod}
        onChange={onChange}
        sx={{ mt: 1 }}
      >
        <FormControlLabel
          value="cash_on_delivery"
          control={<Radio />}
          label={
            <Box>
              <Typography variant="body1">Pago contra entrega</Typography>
              <Typography variant="body2" color="text.secondary">
                Paga en efectivo cuando recibas tu pedido
              </Typography>
            </Box>
          }
        />
        <FormControlLabel
          value="bank_transfer"
          control={<Radio />}
          label={
            <Box>
              <Typography variant="body1">Transferencia bancaria</Typography>
              <Typography variant="body2" color="text.secondary">
                Realiza una transferencia a nuestra cuenta bancaria
              </Typography>
            </Box>
          }
        />
        <FormControlLabel
          value="yape_plin"
          control={<Radio />}
          label={
            <Box>
              <Typography variant="body1">Yape / Plin</Typography>
              <Typography variant="body2" color="text.secondary">
                Pago rápido con billeteras digitales
              </Typography>
            </Box>
          }
        />
      </RadioGroup>
    </FormControl>

    <TextField
      fullWidth
      name="notes"
      label="Notas adicionales (opcional)"
      multiline
      rows={3}
      value={formData.notes}
      onChange={onChange}
      sx={{ mt: 3 }}
      placeholder="Instrucciones especiales para la entrega, horarios preferidos, etc."
    />
  </Box>
);

// Componente para resumen final
const OrderSummary = ({ formData, cartItems, cartTotals }) => (
  <Box>
    <Typography variant="h6" gutterBottom>
      Confirmación del Pedido
    </Typography>

    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle1" gutterBottom>
          Información de Envío
        </Typography>
        <Typography variant="body2">
          {formData.firstName} {formData.lastName}
        </Typography>
        <Typography variant="body2">{formData.email}</Typography>
        <Typography variant="body2">{formData.phone}</Typography>
        <Typography variant="body2">{formData.address}</Typography>
        <Typography variant="body2">
          {formData.district}, {formData.city} {formData.postalCode}
        </Typography>
        {formData.reference && (
          <Typography variant="body2" color="text.secondary">
            Ref: {formData.reference}
          </Typography>
        )}
      </Grid>

      <Grid item xs={12} md={6}>
        <Typography variant="subtitle1" gutterBottom>
          Método de Pago
        </Typography>
        <Typography variant="body2">
          {formData.paymentMethod === 'cash_on_delivery' && 'Pago contra entrega'}
          {formData.paymentMethod === 'bank_transfer' && 'Transferencia bancaria'}
          {formData.paymentMethod === 'yape_plin' && 'Yape / Plin'}
        </Typography>

        {formData.notes && (
          <>
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
              Notas Adicionales
            </Typography>
            <Typography variant="body2">{formData.notes}</Typography>
          </>
        )}
      </Grid>

      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom>
          Productos
        </Typography>
        <List dense>
          {cartItems.map((item) => (
            <ListItem key={item.id} sx={{ px: 0 }}>
              <ListItemText
                primary={item.name}
                secondary={`Cantidad: ${item.quantity} x S/ ${item.price.toFixed(2)}`}
              />
              <Typography variant="body2">
                S/ {(item.price * item.quantity).toFixed(2)}
              </Typography>
            </ListItem>
          ))}
        </List>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography>Subtotal:</Typography>
          <Typography>S/ {cartTotals.subtotal.toFixed(2)}</Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography>IGV (18%):</Typography>
          <Typography>S/ {cartTotals.tax.toFixed(2)}</Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography>Envío:</Typography>
          <Typography>
            {cartTotals.shipping === 0 ? 'Gratis' : `S/ ${cartTotals.shipping.toFixed(2)}`}
          </Typography>
        </Box>
        <Divider sx={{ my: 1 }} />
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h6">Total:</Typography>
          <Typography variant="h6" color="primary">
            S/ {cartTotals.total.toFixed(2)}
          </Typography>
        </Box>
      </Grid>
    </Grid>
  </Box>
);

export default Checkout;

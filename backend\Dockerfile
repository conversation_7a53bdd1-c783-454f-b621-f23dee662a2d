FROM node:18-alpine

# Instalar dependencias del sistema necesarias
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    vips-dev \
    dumb-init

# Crear directorio de la aplicación
WORKDIR /app

# Configurar npm para optimizar instalación
RUN npm config set fund false
RUN npm config set audit false

# Copiar archivos de package.json primero (para mejor cache de Docker)
COPY package*.json ./

# Limpiar cache de npm y instalar dependencias
RUN npm cache clean --force
RUN npm install --no-audit --no-fund

# Copiar código fuente (excluyendo node_modules gracias a .dockerignore)
COPY . .

# Crear directorio para uploads
RUN mkdir -p uploads/products uploads/temp

# Crear usuario no-root (para producción)
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Cambiar ownership de directorios
RUN chown -R nextjs:nodejs /app

# No cambiar a usuario no-root aquí - se maneja en docker-compose
# USER nextjs

# Exponer puerto
EXPOSE 3001

# Variables de entorno por defecto
ENV NODE_ENV=production
ENV PORT=3001

# Comando de inicio con dumb-init para mejor manejo de señales
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]

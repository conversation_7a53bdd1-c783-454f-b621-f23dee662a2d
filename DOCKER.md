# 🐳 Docker Setup - Botica Fray Martin

Esta guía explica cómo usar Docker para desarrollar y desplegar el proyecto Botica Fray Martin.

## 📋 Prerrequisitos

- Docker Engine 20.10+
- Docker Compose 2.0+
- WSL2 (si usas Windows)

## 🚀 Inicio Rápido

### Producción (Recomendado)

```bash
# Clonar y configurar
git clone <repository>
cd botica-fray-martin
cp .env.example .env

# Editar variables de entorno
nano .env

# Construir y ejecutar
docker-compose up --build -d

# Verificar estado
docker-compose ps
```

### Desarrollo

```bash
# Usar configuración de desarrollo
docker-compose -f docker-compose.dev.yml up --build -d

# Ver logs en tiempo real
docker-compose -f docker-compose.dev.yml logs -f
```

## 🛠 Script de Utilidades

Hemos incluido un script para facilitar las operaciones comunes:

```bash
# Hacer ejecutable (solo la primera vez)
chmod +x scripts/docker-utils.sh

# Ver ayuda
./scripts/docker-utils.sh

# Ejemplos de uso
./scripts/docker-utils.sh up-prod      # Producción
./scripts/docker-utils.sh up-dev       # Desarrollo
./scripts/docker-utils.sh status       # Ver estado
./scripts/docker-utils.sh clean        # Limpiar
```

## 📁 Estructura de Archivos Docker

```
├── docker-compose.yml          # Configuración de producción
├── docker-compose.dev.yml      # Configuración de desarrollo
├── .dockerignore               # Archivos ignorados globalmente
├── frontend/
│   ├── Dockerfile              # Build de producción
│   ├── Dockerfile.dev          # Build de desarrollo
│   └── .dockerignore           # Archivos ignorados del frontend
├── backend/
│   ├── Dockerfile              # Build del backend
│   └── .dockerignore           # Archivos ignorados del backend
└── scripts/
    └── docker-utils.sh         # Script de utilidades
```

## 🔧 Configuraciones

### Producción vs Desarrollo

| Aspecto | Producción | Desarrollo |
|---------|------------|------------|
| **Build** | Multi-stage optimizado | Single-stage con herramientas |
| **Volúmenes** | Solo datos persistentes | Código fuente montado |
| **Hot Reload** | ❌ | ✅ |
| **Source Maps** | ❌ | ✅ |
| **Optimización** | ✅ | ❌ |
| **Tamaño imagen** | Mínimo | Mayor |

### Variables de Entorno

Las variables se configuran en `.env`:

```bash
# Base de datos
DB_NAME=botica_fray_martin
DB_USER=postgres
DB_PASSWORD=your_secure_password

# APIs externas
OPENAI_API_KEY=sk-your-openai-key
STRIPE_SECRET_KEY=sk_test_your-stripe-key
WHATSAPP_TOKEN=your-whatsapp-token

# URLs
REACT_APP_API_URL=http://localhost:3001/api
```

## 🏗 Proceso de Build Optimizado

### Frontend
1. **Instala dependencias** dentro del contenedor
2. **Copia código fuente** (excluyendo node_modules)
3. **Compila aplicación** React optimizada
4. **Sirve con Nginx** en imagen mínima

### Backend
1. **Instala dependencias** de producción
2. **Copia código fuente** (excluyendo node_modules)
3. **Configura usuario** no-root para seguridad
4. **Ejecuta aplicación** Node.js

## 📊 Servicios

| Servicio | Puerto | Descripción |
|----------|--------|-------------|
| **frontend** | 3000 | React App con Nginx |
| **backend** | 3001 | API Node.js/Express |
| **postgres** | 5432 | Base de datos PostgreSQL |
| **redis** | 6379 | Cache y sesiones |

## 🔍 Comandos Útiles

### Logs y Debugging

```bash
# Ver logs de todos los servicios
docker-compose logs -f

# Ver logs de un servicio específico
docker-compose logs -f backend

# Entrar al contenedor backend
docker exec -it botica-backend sh

# Entrar al contenedor de base de datos
docker exec -it botica-postgres psql -U postgres -d botica_fray_martin
```

### Gestión de Datos

```bash
# Backup de base de datos
docker exec botica-postgres pg_dump -U postgres botica_fray_martin > backup.sql

# Restaurar backup
docker exec -i botica-postgres psql -U postgres botica_fray_martin < backup.sql

# Ver volúmenes
docker volume ls

# Limpiar volúmenes no utilizados
docker volume prune
```

### Monitoreo

```bash
# Estado de contenedores
docker-compose ps

# Uso de recursos
docker stats

# Inspeccionar red
docker network inspect botica-fray-martin_botica-network
```

## 🚨 Solución de Problemas

### Error: node_modules copiados del host

**Problema**: Docker copia node_modules del host causando lentitud.

**Solución**: Los archivos `.dockerignore` previenen esto. Si persiste:

```bash
# Limpiar node_modules locales
rm -rf frontend/node_modules backend/node_modules

# Rebuild sin cache
docker-compose build --no-cache
```

### Error: Permisos en WSL

**Problema**: Problemas de permisos en Windows/WSL.

**Solución**:

```bash
# Cambiar ownership en WSL
sudo chown -R $USER:$USER .

# O usar el script de utilidades
./scripts/docker-utils.sh clean-all
```

### Error: Puerto en uso

**Problema**: Puerto 3000 o 3001 ya está en uso.

**Solución**:

```bash
# Cambiar puertos en .env
FRONTEND_PORT=3002
BACKEND_PORT=3003

# O detener servicios conflictivos
sudo lsof -ti:3000 | xargs kill -9
```

### Error: Base de datos no conecta

**Problema**: Backend no puede conectar a PostgreSQL.

**Solución**:

```bash
# Verificar que postgres esté corriendo
docker-compose ps postgres

# Ver logs de postgres
docker-compose logs postgres

# Reiniciar servicios
docker-compose restart postgres backend
```

## 🔒 Seguridad

### Producción

- ✅ Usuario no-root en contenedores
- ✅ Variables de entorno seguras
- ✅ Imágenes mínimas (Alpine)
- ✅ Sin herramientas de desarrollo
- ✅ Secrets management

### Desarrollo

- ⚠️ Usuario root para evitar problemas de permisos
- ⚠️ Herramientas de desarrollo incluidas
- ⚠️ Hot reload habilitado

## 📈 Optimizaciones

### Build Cache

Los Dockerfiles están optimizados para aprovechar el cache:

1. **Dependencias primero**: `package.json` se copia antes que el código
2. **Layers separados**: Cada comando RUN es un layer cacheable
3. **Multi-stage**: Frontend usa build multi-stage para imagen final mínima

### Tamaño de Imagen

| Imagen | Tamaño Aprox. |
|--------|---------------|
| Frontend (producción) | ~50MB |
| Backend (producción) | ~200MB |
| PostgreSQL | ~200MB |
| Redis | ~30MB |

## 🚀 Despliegue

### Desarrollo Local

```bash
./scripts/docker-utils.sh up-dev
```

### Staging/Producción

```bash
./scripts/docker-utils.sh up-prod
```

### CI/CD

El proyecto está preparado para CI/CD con:

- ✅ Builds reproducibles
- ✅ Variables de entorno configurables
- ✅ Health checks
- ✅ Graceful shutdown

## 📞 Soporte

Si tienes problemas:

1. **Revisa los logs**: `docker-compose logs -f`
2. **Verifica el estado**: `./scripts/docker-utils.sh status`
3. **Limpia y rebuild**: `./scripts/docker-utils.sh clean && ./scripts/docker-utils.sh build-prod`
4. **Consulta este documento** para problemas comunes

---

**¡Listo para desarrollar!** 🎉

El setup de Docker está optimizado para que puedas enfocarte en el código sin preocuparte por la infraestructura.

# Node modules (se instal<PERSON><PERSON> dentro del contenedor)
**/node_modules
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# Build outputs (se generarán dentro del contenedor)
**/build
**/dist
**/.next

# Environment files
**/.env
**/.env.local
**/.env.development.local
**/.env.test.local
**/.env.production.local

# IDE files
**/.vscode
**/.idea
**/*.swp
**/*.swo

# OS generated files
**/.DS_Store
**/.DS_Store?
**/._*
**/.Spotlight-V100
**/.Trashes
**/ehthumbs.db
**/Thumbs.db

# Git
.git
.gitignore
**/.gitignore

# Docker
**/Dockerfile
**/.dockerignore

# Logs
**/logs
**/*.log

# Coverage directory
**/coverage

# ESLint cache
**/.eslintcache

# Optional npm cache directory
**/.npm

# Optional REPL history
**/.node_repl_history

# Output of 'npm pack'
**/*.tgz

# Yarn Integrity file
**/.yarn-integrity

# Cache directories
**/.cache
**/.parcel-cache

# Serverless directories
**/.serverless

# FuseBox cache
**/.fusebox/

# DynamoDB Local files
**/.dynamodb/

# TernJS port file
**/.tern-port

# VSCode test files
**/.vscode-test

# Uploads directory (se creará en el contenedor)
**/uploads

# Temporary files
**/tmp
**/temp

# Test files
**/test
**/tests
**/__tests__

# Documentation
**/docs
**/*.md

# CI/CD
**/.github
**/.gitlab-ci.yml
**/.travis.yml

# Database files
**/*.sqlite
**/*.db

# Backup files
**/*.bak
**/*.backup

# Lock files (se regenerarán)
**/package-lock.json
**/yarn.lock

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const RedisStore = require('connect-redis').default;
const { createClient } = require('redis');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

// Importar configuraciones y utilidades
const db = require('./config/database');
const logger = require('./utils/logger');
const { globalErrorHandler } = require('./middleware/errorHandler');
const { authenticateToken } = require('./middleware/auth');
const defineAssociations = require('./models/associations');

// Importar rutas
const authRoutes = require('./routes/auth');
const productRoutes = require('./routes/products');
const categoryRoutes = require('./routes/categories');
const orderRoutes = require('./routes/orders');
const customerRoutes = require('./routes/customers');
const supplierRoutes = require('./routes/suppliers');
const inventoryRoutes = require('./routes/inventory');
const paymentRoutes = require('./routes/payments');
const promotionRoutes = require('./routes/promotions');
const dashboardRoutes = require('./routes/dashboard');
const chatbotRoutes = require('./routes/chatbot');
const uploadRoutes = require('./routes/uploads');
const systemRoutes = require('./routes/system');
const contactRoutes = require('./routes/contact');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Configurar Redis cliente
let redisClient;
if (process.env.NODE_ENV === 'production') {
  redisClient = createClient({
    url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`
  });
} else {
  redisClient = createClient({
    url: `redis://${process.env.REDIS_HOST || 'redis'}:${process.env.REDIS_PORT || 6379}`
  });
}

redisClient.on('error', (err) => {
  logger.error('Redis Client Error', err);
});

// Middleware de seguridad
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "fonts.googleapis.com"],
      fontSrc: ["'self'", "fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // limitar cada IP a 100 requests por windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false
});
app.use('/api/', limiter);

// CORS
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Compression
app.use(compression());

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Sesiones
app.use(session({
  store: new RedisStore({ client: redisClient }),
  secret: process.env.JWT_SECRET || 'botica-fray-martin-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 horas
  }
}));

// Servir archivos estáticos
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Logging de requests
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/inventory', inventoryRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/promotions', promotionRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/chatbot', chatbotRoutes);
app.use('/api/uploads', uploadRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/contact', contactRoutes);

// Socket.IO para notificaciones en tiempo real
io.use((socket, next) => {
  // Middleware de autenticación para websockets si es necesario
  next();
});

io.on('connection', (socket) => {
  logger.info('User connected:', socket.id);
  
  socket.on('join-admin', () => {
    socket.join('admin-room');
    logger.info('Admin joined admin room');
  });
  
  socket.on('disconnect', () => {
    logger.info('User disconnected:', socket.id);
  });
});

// Hacer io disponible globalmente para otros módulos
app.set('io', io);

// Middleware para manejar 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found'
  });
});

// Error handler global
app.use(globalErrorHandler);

// Función para inicializar la aplicación
async function startServer() {
  try {
    // Conectar a Redis
    await redisClient.connect();
    logger.info('Connected to Redis');
    
    // Sincronizar base de datos
    await db.authenticate();
    logger.info('Connected to PostgreSQL database');
    
    // Definir asociaciones de modelos
    defineAssociations();
    logger.info('Model associations defined');
    
    // En desarrollo, sincronizar modelos
    if (process.env.NODE_ENV === 'development') {
      // Verificar si las tablas existen y tienen datos
      const User = require('./models/User');
      let shouldForceSync = false;

      try {
        const userCount = await User.count();
        if (userCount === 0) {
          shouldForceSync = true;
        }
      } catch (error) {
        // Si hay error, probablemente las tablas no existen
        shouldForceSync = true;
      }

      if (shouldForceSync) {
        await db.sync({ force: true });
        logger.info('Database synchronized (force recreated)');

        // Ejecutar seeder para datos iniciales
        await seedInitialData();
        logger.info('Initial data seeded');
      } else {
        await db.sync({ alter: true });
        logger.info('Database synchronized (alter mode)');

        // Verificar si necesitamos datos iniciales
        const User = require('./models/User');
        const Product = require('./models/Product');

        const userCount = await User.count();
        const productCount = await Product.count();

        if (userCount === 0 || productCount < 11) { // Esperamos 11 productos
          logger.info('Seeding initial data...');
          await seedInitialData();
          logger.info('Initial data seeded');
        } else {
          logger.info('Database already has sufficient data, skipping seed');
        }
      }
    }
    
    // Iniciar servidor
    server.listen(PORT, () => {
      logger.info(`Server is running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV}`);
      logger.info(`API Documentation available at http://localhost:${PORT}/api-docs`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Manejar cierre graceful
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await redisClient.quit();
  await db.close();
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await redisClient.quit();
  await db.close();
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

// Función para crear solo productos
async function createProducts() {
  const Category = require('./models/Category');
  const Product = require('./models/Product');

  // Obtener categorías existentes
  const analgesicos = await Category.findOne({ where: { name: 'Analgésicos' } });
  const antibioticos = await Category.findOne({ where: { name: 'Antibióticos' } });
  const vitaminas = await Category.findOne({ where: { name: 'Vitaminas y Suplementos' } });
  const cuidadoPersonal = await Category.findOne({ where: { name: 'Cuidado Personal' } });
  const primerosAuxilios = await Category.findOne({ where: { name: 'Primeros Auxilios' } });
  const equiposMedicos = await Category.findOne({ where: { name: 'Equipos Médicos' } });
  const respiratorio = await Category.findOne({ where: { name: 'Respiratorio' } });
  const dermatologia = await Category.findOne({ where: { name: 'Dermatología' } });

  if (!analgesicos || !vitaminas || !cuidadoPersonal || !primerosAuxilios) {
    logger.error('Required categories not found, cannot create products');
    return;
  }

  // Limpiar productos existentes
  await Product.destroy({ where: {} });

  // Crear todos los productos del archivo SQL
  await Product.bulkCreate([
    // Analgésicos
    {
      sku: 'PARA500',
      name: 'Paracetamol 500mg',
      description: 'Analgésico y antipirético para el alivio del dolor leve a moderado y la fiebre',
      short_description: 'Analgésico para dolor y fiebre',
      category_id: analgesicos.id,
      price: 2.50,
      cost_price: 1.80,
      stock_quantity: 100,
      min_stock_level: 20,
      max_stock_level: 200,
      manufacturer: 'Laboratorios Unidos',
      presentation: 'Tabletas x 20',
      is_active: true,
      is_featured: true,
      meta_title: 'Paracetamol 500mg - Alivio del dolor',
      meta_description: 'Paracetamol 500mg para el alivio efectivo del dolor y fiebre'
    },
    {
      sku: 'IBU400',
      name: 'Ibuprofeno 400mg',
      description: 'Antiinflamatorio no esteroideo para dolor, inflamación y fiebre',
      short_description: 'Antiinflamatorio para dolor e inflamación',
      category_id: analgesicos.id,
      price: 8.50,
      cost_price: 6.20,
      stock_quantity: 75,
      min_stock_level: 15,
      max_stock_level: 150,
      manufacturer: 'Farma Plus',
      presentation: 'Tabletas x 30',
      is_active: true,
      is_featured: true,
      meta_title: 'Ibuprofeno 400mg - Antiinflamatorio',
      meta_description: 'Ibuprofeno 400mg para dolor, inflamación y fiebre'
    },
    // Antibióticos
    {
      sku: 'AMOXI500',
      name: 'Amoxicilina 500mg',
      description: 'Antibiótico para infecciones bacterianas respiratorias y urinarias',
      short_description: 'Antibiótico de amplio espectro',
      category_id: antibioticos?.id || analgesicos.id,
      price: 12.00,
      cost_price: 8.50,
      stock_quantity: 60,
      min_stock_level: 10,
      max_stock_level: 120,
      manufacturer: 'Antibióticos SA',
      presentation: 'Cápsulas x 21',
      is_active: true,
      is_featured: false,
      meta_title: 'Amoxicilina 500mg - Antibiótico',
      meta_description: 'Amoxicilina 500mg para tratamiento de infecciones bacterianas'
    },
    // Vitaminas
    {
      sku: 'VITC1000',
      name: 'Vitamina C 1000mg',
      description: 'Suplemento de vitamina C para fortalecer el sistema inmunológico',
      short_description: 'Vitamina C para defensas',
      category_id: vitaminas.id,
      price: 15.00,
      cost_price: 10.50,
      stock_quantity: 80,
      min_stock_level: 15,
      max_stock_level: 160,
      manufacturer: 'Nutri Health',
      presentation: 'Tabletas efervescentes x 30',
      is_active: true,
      is_featured: true,
      meta_title: 'Vitamina C 1000mg - Sistema inmune',
      meta_description: 'Vitamina C 1000mg para fortalecer las defensas naturales'
    },
    {
      sku: 'VITB12',
      name: 'Complejo B',
      description: 'Complejo vitamínico B para el sistema nervioso y energía',
      short_description: 'Vitaminas del complejo B',
      category_id: vitaminas.id,
      price: 18.50,
      cost_price: 13.20,
      stock_quantity: 50,
      min_stock_level: 10,
      max_stock_level: 100,
      manufacturer: 'Vita Complex',
      presentation: 'Cápsulas x 60',
      is_active: true,
      is_featured: false,
      meta_title: 'Complejo B - Energía y sistema nervioso',
      meta_description: 'Complejo vitamínico B para energía y función nerviosa'
    },
    // Cuidado Personal
    {
      sku: 'ALCOGEL70',
      name: 'Alcohol en Gel 70%',
      description: 'Desinfectante de manos con alcohol al 70%',
      short_description: 'Desinfectante de manos',
      category_id: cuidadoPersonal.id,
      price: 8.00,
      cost_price: 5.50,
      stock_quantity: 120,
      min_stock_level: 25,
      max_stock_level: 250,
      manufacturer: 'Higiene Total',
      presentation: 'Frasco 250ml',
      is_active: true,
      is_featured: true,
      meta_title: 'Alcohol en Gel 70% - Desinfectante',
      meta_description: 'Alcohol en gel desinfectante para manos'
    },
    // Primeros Auxilios
    {
      sku: 'AGUAOX',
      name: 'Agua Oxigenada 10 vol',
      description: 'Antiséptico para desinfección de heridas menores',
      short_description: 'Antiséptico para heridas',
      category_id: primerosAuxilios.id,
      price: 3.50,
      cost_price: 2.20,
      stock_quantity: 90,
      min_stock_level: 20,
      max_stock_level: 180,
      manufacturer: 'Antisépticos Med',
      presentation: 'Frasco 120ml',
      is_active: true,
      is_featured: false,
      meta_title: 'Agua Oxigenada - Antiséptico',
      meta_description: 'Agua oxigenada para desinfección de heridas'
    },
    {
      sku: 'GASA5X5',
      name: 'Gasas Estériles 5x5cm',
      description: 'Gasas estériles para curación de heridas',
      short_description: 'Gasas estériles para curaciones',
      category_id: primerosAuxilios.id,
      price: 5.00,
      cost_price: 3.50,
      stock_quantity: 150,
      min_stock_level: 30,
      max_stock_level: 300,
      manufacturer: 'Medical Supply',
      presentation: 'Paquete x 25 unidades',
      is_active: true,
      is_featured: false,
      meta_title: 'Gasas Estériles 5x5cm - Primeros auxilios',
      meta_description: 'Gasas estériles para curación y primeros auxilios'
    },
    // Equipos Médicos
    {
      sku: 'GUANTES',
      name: 'Guantes de Látex',
      description: 'Guantes desechables de látex para examinación',
      short_description: 'Guantes desechables de látex',
      category_id: equiposMedicos?.id || primerosAuxilios.id,
      price: 12.00,
      cost_price: 8.80,
      stock_quantity: 200,
      min_stock_level: 40,
      max_stock_level: 400,
      manufacturer: 'LatexCare',
      presentation: 'Caja x 100 unidades',
      is_active: true,
      is_featured: true,
      meta_title: 'Guantes de Látex - Protección',
      meta_description: 'Guantes desechables de látex para protección'
    },
    // Respiratorio
    {
      sku: 'JARABE200',
      name: 'Jarabe para la Tos',
      description: 'Jarabe expectorante para el alivio de la tos con flemas',
      short_description: 'Jarabe expectorante',
      category_id: respiratorio?.id || analgesicos.id,
      price: 14.50,
      cost_price: 10.20,
      stock_quantity: 45,
      min_stock_level: 10,
      max_stock_level: 90,
      manufacturer: 'Respira Bien',
      presentation: 'Frasco 200ml',
      is_active: true,
      is_featured: false,
      meta_title: 'Jarabe para la Tos - Expectorante',
      meta_description: 'Jarabe expectorante para alivio de la tos'
    },
    // Dermatología
    {
      sku: 'CREMAHID',
      name: 'Crema Hidratante',
      description: 'Crema hidratante para piel seca y sensible',
      short_description: 'Crema para piel seca',
      category_id: dermatologia?.id || cuidadoPersonal.id,
      price: 16.00,
      cost_price: 11.50,
      stock_quantity: 65,
      min_stock_level: 15,
      max_stock_level: 130,
      manufacturer: 'DermaCare',
      presentation: 'Tubo 100g',
      is_active: true,
      is_featured: false,
      meta_title: 'Crema Hidratante - Cuidado de la piel',
      meta_description: 'Crema hidratante para piel seca y sensible'
    }
  ]);

  logger.info('Products created successfully');
}

// Función para crear datos iniciales
async function seedInitialData() {
  const bcrypt = require('bcryptjs');
  const User = require('./models/User');
  const Category = require('./models/Category');
  const Product = require('./models/Product');

  try {
    // Verificar si ya existen datos
    const userCount = await User.count();
    const productCount = await Product.count();

    if (userCount > 0 && productCount >= 11) {
      logger.info('Initial data already exists, skipping seed');
      return;
    }

    // Si hay usuarios pero pocos productos, solo crear productos
    if (userCount > 0 && productCount < 11) {
      logger.info('Users exist but need to create products...');
      await createProducts();
      return;
    }

    // Crear usuario administrador
    const adminPassword = await bcrypt.hash('admin123', 10);
    await User.create({
      email: '<EMAIL>',
      password_hash: adminPassword,
      first_name: 'Administrador',
      last_name: 'Sistema',
      role: 'admin',
      is_active: true,
      email_verified: true
    });

    // Crear categorías
    const categories = await Category.bulkCreate([
      { name: 'Medicamentos', description: 'Medicamentos con y sin receta médica', is_active: true },
      { name: 'Analgésicos', description: 'Medicamentos para el dolor y la inflamación', is_active: true },
      { name: 'Antibióticos', description: 'Medicamentos para infecciones bacterianas', is_active: true },
      { name: 'Vitaminas y Suplementos', description: 'Vitaminas, minerales y suplementos nutricionales', is_active: true },
      { name: 'Cuidado Personal', description: 'Productos de higiene y cuidado personal', is_active: true },
      { name: 'Primeros Auxilios', description: 'Materiales y productos para primeros auxilios', is_active: true },
      { name: 'Equipos Médicos', description: 'Termómetros, tensiómetros y otros equipos', is_active: true },
      { name: 'Cuidado del Bebé', description: 'Productos especializados para bebés', is_active: true },
      { name: 'Dermatología', description: 'Productos para el cuidado de la piel', is_active: true },
      { name: 'Respiratorio', description: 'Productos para afecciones respiratorias', is_active: true }
    ]);

    // Obtener categorías para productos
    const analgesicos = categories.find(c => c.name === 'Analgésicos');
    const antibioticos = categories.find(c => c.name === 'Antibióticos');
    const vitaminas = categories.find(c => c.name === 'Vitaminas y Suplementos');
    const cuidadoPersonal = categories.find(c => c.name === 'Cuidado Personal');
    const primerosAuxilios = categories.find(c => c.name === 'Primeros Auxilios');
    const equiposMedicos = categories.find(c => c.name === 'Equipos Médicos');
    const respiratorio = categories.find(c => c.name === 'Respiratorio');
    const dermatologia = categories.find(c => c.name === 'Dermatología');

    // Crear productos (todos los del archivo SQL)
    await Product.bulkCreate([
      // Analgésicos
      {
        sku: 'PARA500',
        name: 'Paracetamol 500mg',
        description: 'Analgésico y antipirético para el alivio del dolor leve a moderado y la fiebre',
        short_description: 'Analgésico para dolor y fiebre',
        category_id: analgesicos.id,
        price: 2.50,
        cost_price: 1.80,
        stock_quantity: 100,
        min_stock_level: 20,
        max_stock_level: 200,
        manufacturer: 'Laboratorios Unidos',
        presentation: 'Tabletas x 20',
        is_active: true,
        is_featured: true,
        meta_title: 'Paracetamol 500mg - Alivio del dolor',
        meta_description: 'Paracetamol 500mg para el alivio efectivo del dolor y fiebre'
      },
      {
        sku: 'IBU400',
        name: 'Ibuprofeno 400mg',
        description: 'Antiinflamatorio no esteroideo para dolor, inflamación y fiebre',
        short_description: 'Antiinflamatorio para dolor e inflamación',
        category_id: analgesicos.id,
        price: 8.50,
        cost_price: 6.20,
        stock_quantity: 75,
        min_stock_level: 15,
        max_stock_level: 150,
        manufacturer: 'Farma Plus',
        presentation: 'Tabletas x 30',
        is_active: true,
        is_featured: true,
        meta_title: 'Ibuprofeno 400mg - Antiinflamatorio',
        meta_description: 'Ibuprofeno 400mg para dolor, inflamación y fiebre'
      },
      // Antibióticos
      {
        sku: 'AMOXI500',
        name: 'Amoxicilina 500mg',
        description: 'Antibiótico para infecciones bacterianas respiratorias y urinarias',
        short_description: 'Antibiótico de amplio espectro',
        category_id: antibioticos.id,
        price: 12.00,
        cost_price: 8.50,
        stock_quantity: 60,
        min_stock_level: 10,
        max_stock_level: 120,
        manufacturer: 'Antibióticos SA',
        presentation: 'Cápsulas x 21',
        is_active: true,
        is_featured: false,
        meta_title: 'Amoxicilina 500mg - Antibiótico',
        meta_description: 'Amoxicilina 500mg para tratamiento de infecciones bacterianas'
      },
      // Vitaminas
      {
        sku: 'VITC1000',
        name: 'Vitamina C 1000mg',
        description: 'Suplemento de vitamina C para fortalecer el sistema inmunológico',
        short_description: 'Vitamina C para defensas',
        category_id: vitaminas.id,
        price: 15.00,
        cost_price: 10.50,
        stock_quantity: 80,
        min_stock_level: 15,
        max_stock_level: 160,
        manufacturer: 'Nutri Health',
        presentation: 'Tabletas efervescentes x 30',
        is_active: true,
        is_featured: true,
        meta_title: 'Vitamina C 1000mg - Sistema inmune',
        meta_description: 'Vitamina C 1000mg para fortalecer las defensas naturales'
      },
      {
        sku: 'VITB12',
        name: 'Complejo B',
        description: 'Complejo vitamínico B para el sistema nervioso y energía',
        short_description: 'Vitaminas del complejo B',
        category_id: vitaminas.id,
        price: 18.50,
        cost_price: 13.20,
        stock_quantity: 50,
        min_stock_level: 10,
        max_stock_level: 100,
        manufacturer: 'Vita Complex',
        presentation: 'Cápsulas x 60',
        is_active: true,
        is_featured: false,
        meta_title: 'Complejo B - Energía y sistema nervioso',
        meta_description: 'Complejo vitamínico B para energía y función nerviosa'
      },
      // Cuidado Personal
      {
        sku: 'ALCOGEL70',
        name: 'Alcohol en Gel 70%',
        description: 'Desinfectante de manos con alcohol al 70%',
        short_description: 'Desinfectante de manos',
        category_id: cuidadoPersonal.id,
        price: 8.00,
        cost_price: 5.50,
        stock_quantity: 120,
        min_stock_level: 25,
        max_stock_level: 250,
        manufacturer: 'Higiene Total',
        presentation: 'Frasco 250ml',
        is_active: true,
        is_featured: true,
        meta_title: 'Alcohol en Gel 70% - Desinfectante',
        meta_description: 'Alcohol en gel desinfectante para manos'
      },
      // Primeros Auxilios
      {
        sku: 'AGUAOX',
        name: 'Agua Oxigenada 10 vol',
        description: 'Antiséptico para desinfección de heridas menores',
        short_description: 'Antiséptico para heridas',
        category_id: primerosAuxilios.id,
        price: 3.50,
        cost_price: 2.20,
        stock_quantity: 90,
        min_stock_level: 20,
        max_stock_level: 180,
        manufacturer: 'Antisépticos Med',
        presentation: 'Frasco 120ml',
        is_active: true,
        is_featured: false,
        meta_title: 'Agua Oxigenada - Antiséptico',
        meta_description: 'Agua oxigenada para desinfección de heridas'
      },
      {
        sku: 'GASA5X5',
        name: 'Gasas Estériles 5x5cm',
        description: 'Gasas estériles para curación de heridas',
        short_description: 'Gasas estériles para curaciones',
        category_id: primerosAuxilios.id,
        price: 5.00,
        cost_price: 3.50,
        stock_quantity: 150,
        min_stock_level: 30,
        max_stock_level: 300,
        manufacturer: 'Medical Supply',
        presentation: 'Paquete x 25 unidades',
        is_active: true,
        is_featured: false,
        meta_title: 'Gasas Estériles 5x5cm - Primeros auxilios',
        meta_description: 'Gasas estériles para curación y primeros auxilios'
      },
      // Equipos Médicos
      {
        sku: 'GUANTES',
        name: 'Guantes de Látex',
        description: 'Guantes desechables de látex para examinación',
        short_description: 'Guantes desechables de látex',
        category_id: equiposMedicos.id,
        price: 12.00,
        cost_price: 8.80,
        stock_quantity: 200,
        min_stock_level: 40,
        max_stock_level: 400,
        manufacturer: 'LatexCare',
        presentation: 'Caja x 100 unidades',
        is_active: true,
        is_featured: true,
        meta_title: 'Guantes de Látex - Protección',
        meta_description: 'Guantes desechables de látex para protección'
      },
      // Respiratorio
      {
        sku: 'JARABE200',
        name: 'Jarabe para la Tos',
        description: 'Jarabe expectorante para el alivio de la tos con flemas',
        short_description: 'Jarabe expectorante',
        category_id: respiratorio.id,
        price: 14.50,
        cost_price: 10.20,
        stock_quantity: 45,
        min_stock_level: 10,
        max_stock_level: 90,
        manufacturer: 'Respira Bien',
        presentation: 'Frasco 200ml',
        is_active: true,
        is_featured: false,
        meta_title: 'Jarabe para la Tos - Expectorante',
        meta_description: 'Jarabe expectorante para alivio de la tos'
      },
      // Dermatología
      {
        sku: 'CREMAHID',
        name: 'Crema Hidratante',
        description: 'Crema hidratante para piel seca y sensible',
        short_description: 'Crema para piel seca',
        category_id: dermatologia.id,
        price: 16.00,
        cost_price: 11.50,
        stock_quantity: 65,
        min_stock_level: 15,
        max_stock_level: 130,
        manufacturer: 'DermaCare',
        presentation: 'Tubo 100g',
        is_active: true,
        is_featured: false,
        meta_title: 'Crema Hidratante - Cuidado de la piel',
        meta_description: 'Crema hidratante para piel seca y sensible'
      }
    ]);

    logger.info('Initial data created successfully');
  } catch (error) {
    logger.error('Error seeding initial data:', error);
  }
}

// Inicializar servidor
startServer();

module.exports = { app, server, io };

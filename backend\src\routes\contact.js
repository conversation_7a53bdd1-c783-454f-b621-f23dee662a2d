const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');

// Validaciones para el formulario de contacto
const contactValidation = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('El nombre es requerido')
    .isLength({ min: 2, max: 100 })
    .withMessage('El nombre debe tener entre 2 y 100 caracteres'),
  
  body('email')
    .isEmail()
    .withMessage('Debe ser un email válido')
    .normalizeEmail(),
  
  body('phone')
    .optional()
    .isMobilePhone('es-PE')
    .withMessage('Debe ser un número de teléfono válido'),
  
  body('subject')
    .notEmpty()
    .withMessage('El motivo de contacto es requerido')
    .isIn(['general', 'products', 'orders', 'complaints', 'suggestions', 'pharmacy', 'delivery', 'other'])
    .withMessage('Motivo de contacto inválido'),
  
  body('message')
    .trim()
    .notEmpty()
    .withMessage('El mensaje es requerido')
    .isLength({ min: 10, max: 1000 })
    .withMessage('El mensaje debe tener entre 10 y 1000 caracteres')
];

// Enviar mensaje de contacto
const sendContactMessage = async (req, res, next) => {
  try {
    // Verificar errores de validación
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const { name, email, phone, subject, message } = req.body;

    // Log del mensaje de contacto
    logger.info('Contact message received:', {
      name,
      email,
      phone,
      subject,
      messageLength: message.length,
      timestamp: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // En una implementación real, aquí se podría:
    // 1. Guardar en base de datos
    // 2. Enviar email al equipo de soporte
    // 3. Crear ticket en sistema de soporte
    // 4. Enviar confirmación por email al cliente

    // Simular procesamiento
    await new Promise(resolve => setTimeout(resolve, 500));

    // Respuesta exitosa
    res.json({
      success: true,
      message: 'Mensaje enviado exitosamente. Te contactaremos pronto.',
      data: {
        id: `contact_${Date.now()}`,
        name,
        email,
        subject,
        timestamp: new Date(),
        status: 'received'
      }
    });

  } catch (error) {
    logger.error('Error processing contact message:', {
      error: error.message,
      stack: error.stack,
      body: req.body
    });
    next(error);
  }
};

// Obtener información de contacto (público)
const getContactInfo = async (req, res, next) => {
  try {
    const contactInfo = {
      address: {
        street: 'Av. Principal 123',
        city: 'Lima',
        country: 'Perú',
        postalCode: '15001'
      },
      phone: '+51 999 888 777',
      whatsapp: '+51 999 888 777',
      email: '<EMAIL>',
      schedule: {
        monday_friday: '8:00 AM - 8:00 PM',
        saturday: '8:00 AM - 6:00 PM',
        sunday: '9:00 AM - 2:00 PM',
        delivery: '24 horas todos los días'
      },
      social: {
        facebook: 'https://facebook.com/boticafraymartin',
        instagram: 'https://instagram.com/boticafraymartin',
        twitter: 'https://twitter.com/boticafraymartin'
      },
      services: [
        'Consultas farmacéuticas especializadas',
        'Delivery 24 horas en Lima Metropolitana',
        'Medicamentos con y sin receta médica',
        'Productos de cuidado personal',
        'Atención farmacéutica personalizada',
        'Programas de descuentos y promociones'
      ]
    };

    res.json({
      success: true,
      data: contactInfo
    });

  } catch (error) {
    logger.error('Error getting contact info:', error);
    next(error);
  }
};

// Rutas
router.post('/', contactValidation, sendContactMessage);
router.get('/info', getContactInfo);

module.exports = router;

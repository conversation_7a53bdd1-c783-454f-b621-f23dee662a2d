services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: botica-postgres-dev
    environment:
      POSTGRES_DB: ${DB_NAME:-botica_fray_martin_dev}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password123}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - botica-dev-network

  # Redis para sesiones y cache
  redis:
    image: redis:7-alpine
    container_name: botica-redis-dev
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - botica-dev-network

  # Backend API (modo desarrollo)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: botica-backend-dev
    user: "0:0"  # Run as root in development to avoid volume permission issues
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-botica_fray_martin_dev}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-password123}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-your-jwt-secret-key-dev}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      WHATSAPP_TOKEN: ${WHATSAPP_TOKEN}
      WHATSAPP_PHONE_NUMBER: ${WHATSAPP_PHONE_NUMBER}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      YAPE_API_KEY: ${YAPE_API_KEY}
      PLIN_API_KEY: ${PLIN_API_KEY}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASSWORD: ${EMAIL_PASSWORD}
      ADMIN_WHATSAPP_NUMBERS: ${ADMIN_WHATSAPP_NUMBERS}
    depends_on:
      - postgres
      - redis
    volumes:
      # Volúmenes para desarrollo con hot reload
      - ./backend:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
    networks:
      - botica-dev-network
    command: sh -c "npm install nodemon && npm run dev"

  # Frontend React (modo desarrollo)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: botica-frontend-dev
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-/api}
      REACT_APP_STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
      PROXY_TARGET: http://backend:3001
      CHOKIDAR_USEPOLLING: true
    depends_on:
      - backend
    volumes:
      # Volúmenes para desarrollo con hot reload
      - ./frontend:/app
      - /app/node_modules
    networks:
      - botica-dev-network

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  botica-dev-network:
    driver: bridge

const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Configurar proxy para las rutas de API
  const target = process.env.PROXY_TARGET || 'http://backend:3001';
  
  app.use(
    '/api',
    createProxyMiddleware({
      target: target,
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      onError: (err, req, res) => {
        console.error('Proxy error:', err);
        res.status(500).send('Proxy error');
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying ${req.method} ${req.url} to ${target}${req.url}`);
      }
    })
  );
};

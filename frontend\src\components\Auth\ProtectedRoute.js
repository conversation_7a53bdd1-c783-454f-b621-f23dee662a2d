import React from 'react';
import { Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';

const ProtectedRoute = ({ children, requiredRole }) => {
  const { isAuthenticated, user } = useSelector(state => state.auth);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && !checkRole(user?.role, requiredRole)) {
    return <Navigate to="/" replace />;
  }

  return children;
};

const checkRole = (userRole, requiredRole) => {
  const roleHierarchy = {
    'customer': ['customer'],
    'employee': ['customer', 'employee'],
    'manager': ['customer', 'employee', 'manager'],
    'admin': ['customer', 'employee', 'manager', 'admin']
  };
  
  return roleHierarchy[userRole]?.includes(requiredRole) || false;
};

export default ProtectedRoute;
